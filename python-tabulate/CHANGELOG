- 0.10.0: Add support for Python 3.11, 3.12, 3.13.
  Drop support for Python 3.7, 3.8.
  PRESERVE_STERILITY global is replaced with preserve_sterility function argument.
  New formatting options: headersglobalalign, headersalign, colglobalalign.
  New output format: ``colon_grid`` (Pandoc grid_tables with alignment)
  Various bug fixes.
  Improved error messages.
- 0.9.0: Drop support for Python 2.7, 3.5, 3.6.
  Migrate to pyproject.toml project layout (PEP 621).
  New output formats: `asciidoc`, various `*grid` and `*outline` formats.
  New output features: vertical row alignment, separating lines.
  New input format: list of dataclasses (Python 3.7 or later).
  Support infinite iterables as row indices.
  Improve column width options.
  Improve support for ANSI escape sequences and document the behavior.
  Various bug fixes.
- 0.8.10: Python 3.10 support. Bug fixes. Column width parameter.
- 0.8.9: Bug fix. Revert support of decimal separators.
- 0.8.8: Python 3.9 support, 3.10 ready.
  New formats: ``unsafehtml``, ``latex_longtable``, ``fancy_outline``.
  Support lists of UserDicts as input.
  Support hyperlinks in terminal output.
  Improve testing on systems with proxies.
  Migrate to pytest.
  Various bug fixes and improvements.
- 0.8.7: Bug fixes. New format: `pretty`. HTML escaping.
- 0.8.6: Bug fixes. Stop supporting Python 3.3, 3.4.
- 0.8.5: Fix broken Windows package. Minor documentation updates.
- 0.8.4: Bug fixes.
- 0.8.3: New formats: `github`. Custom column alignment. Bug fixes.
- 0.8.2: Bug fixes.
- 0.8.1: Multiline data in several output formats.
  New ``latex_raw`` format.
  Column-specific floating point formatting.
  Python 3.5 & 3.6 support. Drop support for Python 2.6, 3.2, 3.3 (should still work).
- 0.7.7: Identical to 0.7.6, resolving some PyPI issues.
- 0.7.6: Bug fixes. New table formats (``psql``, ``jira``, ``moinmoin``, ``textile``).
  Wide character support. Printing from database cursors.
  Option to print row indices. Boolean columns. Ragged rows.
  Option to disable number parsing.
- 0.7.5: Bug fixes. ``--float`` format option for the command line utility.
- 0.7.4: Bug fixes. ``fancy_grid`` and ``html`` formats. Command line utility.
- 0.7.3: Bug fixes. Python 3.4 support. Iterables of dicts. ``latex_booktabs`` format.
- 0.7.2: Python 3.2 support.
- 0.7.1: Bug fixes. ``tsv`` format. Column alignment can be disabled.
- 0.7:   ``latex`` tables. Printing lists of named tuples and NumPy
  record arrays. Fix printing date and time values. Python <= 2.6.4 is supported.
- 0.6:   ``mediawiki`` tables, bug fixes.
- 0.5.1: Fix README.rst formatting. Optimize (performance similar to 0.4.4).
- 0.5:   ANSI color sequences. Printing dicts of iterables and Pandas' dataframes.
- 0.4.4: Python 2.6 support.
- 0.4.3: Bug fix, None as a missing value.
- 0.4.2: Fix manifest file.
- 0.4.1: Update license and documentation.
- 0.4:   Unicode support, Python3 support, ``rst`` tables.
- 0.3:   Initial PyPI release. Table formats: ``simple``, ``plain``,
  ``grid``, ``pipe``, and ``orgtbl``.

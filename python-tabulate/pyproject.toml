[build-system]
requires = ["setuptools>=77.0.3", "setuptools_scm[toml]>=3.4.3"]
build-backend = "setuptools.build_meta"

[project]
name = "tabulate"
authors = [{name = "<PERSON>", email = "<EMAIL>"}]
license = "MIT"
license-files = ["LICENSE"]
description = "Pretty-print tabular data"
readme = "README.md"
classifiers = [
    "Development Status :: 4 - Beta",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries",
]
requires-python = ">=3.9"
dynamic = ["version"]

[project.urls]
Homepage = "https://github.com/astanin/python-tabulate"

[project.optional-dependencies]
widechars = ["wcwidth"]

[project.scripts]
tabulate = "tabulate:_main"

[tool.setuptools]
packages = ["tabulate"]

[tool.setuptools_scm]
write_to = "tabulate/version.py"

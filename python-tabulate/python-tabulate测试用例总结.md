# Python-Tabulate 测试用例总结

## 测试用例统计概览

| 测试文件 | 测试函数数量 | 主要测试内容 | 行数范围 |
|---------|-------------|-------------|----------|
| test_output.py | 199 | 输出格式测试 | 1-3351 |
| test_input.py | 42 | 输入数据处理测试 | 1-1200 |
| test_internal.py | 25 | 内部函数测试 | 1-800 |
| test_api.py | 3 | API接口测试 | 1-50 |
| test_cli.py | 7 | 命令行工具测试 | 1-200 |
| test_regression.py | 35 | 回归测试 | 1-1000 |
| test_textwrapper.py | 14 | 文本包装测试 | 1-400 |
| **总计** | **325** | **全功能覆盖** | **约7000行** |

## 核心功能测试用例详细分析

### 1. 基础表格格式化测试

| 测试函数 | 输入数据类型 | 输出格式 | 测试目的 | 数值类型 |
|---------|-------------|----------|----------|----------|
| `test_plain()` | 列表的列表 | plain格式字符串 | 基础plain格式输出 | 字符串、浮点数 |
| `test_plain_headerless()` | 列表的列表 | plain格式字符串 | 无表头plain格式 | 字符串、整数 |
| `test_simple()` | 列表的列表 | simple格式字符串 | 基础simple格式输出 | 字符串、浮点数 |
| `test_simple_headerless()` | 列表的列表 | simple格式字符串 | 无表头simple格式 | 字符串、整数 |

**输入示例**：
```python
data = [["spam", 41.9999], ["eggs", "451.0"]]
headers = ["strings", "numbers"]
```

**输出示例**：
```
strings      numbers
---------  ---------
spam         41.9999
eggs        451
```

### 2. 多种表格格式支持测试

| 测试函数 | 输入数据类型 | 输出格式 | 测试目的 | 数值类型 |
|---------|-------------|----------|----------|----------|
| `test_grid()` | 列表的列表 | grid格式字符串 | 网格表格格式 | 字符串、浮点数 |
| `test_github()` | 列表的列表 | github格式字符串 | GitHub Markdown格式 | 字符串、浮点数 |
| `test_html()` | 列表的列表 | HTML字符串 | HTML表格格式 | 字符串、浮点数 |
| `test_latex()` | 列表的列表 | LaTeX字符串 | LaTeX表格格式 | 字符串、浮点数 |
| `test_rst()` | 列表的列表 | reStructuredText字符串 | RST表格格式 | 字符串、浮点数 |
| `test_fancy_grid()` | 列表的列表 | fancy_grid格式字符串 | 装饰性网格格式 | 字符串、浮点数 |
| `test_pipe()` | 列表的列表 | pipe格式字符串 | 管道分隔格式 | 字符串、浮点数 |
| `test_orgtbl()` | 列表的列表 | orgtbl格式字符串 | Org-mode表格格式 | 字符串、浮点数 |
| `test_psql()` | 列表的列表 | psql格式字符串 | PostgreSQL格式 | 字符串、浮点数 |
| `test_presto()` | 列表的列表 | presto格式字符串 | Presto SQL格式 | 字符串、浮点数 |

**输入示例**：
```python
data = [["spam", 41.9999], ["eggs", "451.0"]]
headers = ["strings", "numbers"]
```

**Grid格式输出示例**：
```
+-----------+-----------+
| strings   |   numbers |
+===========+===========+
| spam      |   41.9999 |
+-----------+-----------+
| eggs      |  451      |
+-----------+-----------+
```

### 3. 多行单元格处理测试

| 测试函数 | 输入数据类型 | 输出格式 | 测试目的 | 数值类型 |
|---------|-------------|----------|----------|----------|
| `test_plain_multiline()` | 包含换行符的列表 | plain格式多行字符串 | 多行单元格plain格式 | 多行字符串、整数 |
| `test_grid_multiline()` | 包含换行符的列表 | grid格式多行字符串 | 多行单元格grid格式 | 多行字符串、整数 |
| `test_simple_multiline()` | 包含换行符的列表 | simple格式多行字符串 | 多行单元格simple格式 | 多行字符串、整数 |
| `test_fancy_grid_multiline()` | 包含换行符的列表 | fancy_grid格式多行字符串 | 多行单元格装饰格式 | 多行字符串、整数 |

**输入示例**：
```python
table = [[2, "foo\nbar"]]
headers = ["item\nname", "qty"]
```

**输出示例**：
```
+--------+-------+
| item   |   qty |
| name   |       |
+========+=======+
| 2      |   foo |
|        |   bar |
+--------+-------+
```

### 4. 数据输入类型处理测试

| 测试函数 | 输入数据类型 | 输出格式 | 测试目的 | 数值类型 |
|---------|-------------|----------|----------|----------|
| `test_list_of_dicts()` | 字典列表 | 表格字符串 | 字典数据处理 | 字符串、整数 |
| `test_list_of_namedtuples()` | 命名元组列表 | 表格字符串 | 命名元组数据处理 | 字符串、整数 |
| `test_py37orlater_list_of_dataclasses_keys()` | 数据类列表 | 表格字符串 | 数据类数据处理 | 字符串、整数 |
| `test_iterable_of_iterables()` | 可迭代对象 | 表格字符串 | 通用可迭代对象处理 | 混合类型 |
| `test_dict_like()` | 字典形式数据 | 表格字符串 | 字典形式数据处理 | 字符串、数字 |

**输入示例**：
```python
# 字典列表
data = [{"name": "Alice", "age": 24}, {"name": "Bob", "age": 19}]

# 命名元组
from collections import namedtuple
Person = namedtuple("Person", ["name", "age"])
data = [Person("Alice", 24), Person("Bob", 19)]
```

**输出示例**：
```
name      age
------  -----
Alice      24
Bob        19
```

### 5. 列对齐和格式化测试

| 测试函数 | 输入数据类型 | 输出格式 | 测试目的 | 数值类型 |
|---------|-------------|----------|----------|----------|
| `test_align_column_decimal()` | 数字字符串列表 | 对齐的表格字符串 | 小数点对齐测试 | 浮点数字符串 |
| `test_column_global_and_specific_alignment()` | 数字列表 | 对齐的表格字符串 | 全局和特定对齐 | 整数 |
| `test_headers_global_and_specific_alignment()` | 数字列表 | 对齐的表格字符串 | 表头对齐测试 | 整数 |
| `test_column_alignment()` | 混合数据 | 对齐的表格字符串 | 自定义对齐测试 | 字符串、整数 |

**输入示例**：
```python
# 小数点对齐
column = ["12.345", "-1234.5", "1.23", "1234.5"]

# 自定义对齐
data = [[1, 2, 3, 4], [111, 222, 333, 444]]
```

**输出示例**：
```
   12.345
-1234.5
    1.23
 1234.5
```

### 6. 数字格式化处理测试

| 测试函数 | 输入数据类型 | 输出格式 | 测试目的 | 数值类型 |
|---------|-------------|----------|----------|----------|
| `test_floatfmt()` | 浮点数列表 | 格式化的表格字符串 | 浮点数格式化 | 浮点数 |
| `test_intfmt()` | 整数列表 | 格式化的表格字符串 | 整数格式化 | 整数 |
| `test_missingval()` | 包含None的列表 | 替换缺失值的表格字符串 | 缺失值处理 | 混合类型、None |
| `test_floatfmt_multi()` | 多列浮点数 | 多列格式化的表格字符串 | 多列浮点格式化 | 浮点数 |

**输入示例**：
```python
# 浮点数格式化
data = [["pi", 3.141593], ["e", 2.718282]]

# 缺失值处理
data = [["spam", 1, None], ["eggs", 42, 3.14]]
```

**输出示例**：
```
# floatfmt=".4f"
--  ------
pi  3.1416
e   2.7183

# missingval="?"
-----  --  ----
spam    1  ?
eggs   42  3.14
```

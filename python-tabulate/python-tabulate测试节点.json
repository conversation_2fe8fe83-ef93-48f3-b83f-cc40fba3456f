{"测试节点映射": [{"节点编号": 1, "节点名称": "基础表格格式化", "测试文件": ["test_output.py", "test_api.py"], "测试函数": ["test_plain", "test_plain_headerless", "test_simple", "test_tabulate_formats"], "起始行号": [17, 26, 235, 16], "结束行号": [24, 30, 247, 22], "重叠节点": [], "重叠函数": [], "备注": "基础的表格格式化功能，包括plain和simple格式"}, {"节点编号": 2, "节点名称": "多种表格格式支持", "测试文件": ["test_output.py"], "测试函数": ["test_grid", "test_github", "test_html", "test_latex", "test_rst", "test_fancy_grid", "test_pipe", "test_presto", "test_orgtbl", "test_psql", "test_pretty", "test_jira", "test_mediawiki", "test_textile"], "起始行号": [529, 498, 2587, 2667, 2384, 1339, 1967, 1990, 2079, 2131, 2237, 2362, 2504, 2761], "结束行号": [544, 510, 2606, 2685, 2398, 1354, 1979, 2002, 2091, 2145, 2251, 2374, 2521, 2769], "重叠节点": [3], "重叠函数": ["test_grid_multiline", "test_fancy_grid_multiline"], "备注": "支持27种不同的表格输出格式"}, {"节点编号": 3, "节点名称": "多行单元格处理", "测试文件": ["test_output.py", "test_internal.py"], "测试函数": ["test_plain_multiline", "test_grid_multiline", "test_fancy_grid_multiline", "test_multiline_width"], "起始行号": [43, 603, 1413, 8], "结束行号": [57, 620, 1430, 14], "重叠节点": [2], "重叠函数": ["test_grid_multiline", "test_fancy_grid_multiline"], "备注": "处理包含换行符的多行单元格内容"}, {"节点编号": 4, "节点名称": "数据输入类型处理", "测试文件": ["test_input.py"], "测试函数": ["test_list_of_dicts", "test_list_of_namedtuples", "test_py37orlater_list_of_dataclasses_keys", "test_iterable_of_iterables", "test_dict_like"], "起始行号": [200, 180, 240, 13, 120], "结束行号": [210, 190, 250, 21, 130], "重叠节点": [], "重叠函数": [], "备注": "支持多种数据输入类型的处理和转换"}, {"节点编号": 5, "节点名称": "列对齐和格式化", "测试文件": ["test_internal.py", "test_output.py"], "测试函数": ["test_align_column_decimal", "test_align_column_none", "test_column_global_and_specific_alignment", "test_headers_global_and_specific_alignment"], "起始行号": [16, 61, 2919, 2936], "结束行号": [28, 66, 2934, 2961], "重叠节点": [6], "重叠函数": ["test_column_alignment"], "备注": "实现智能的列对齐功能，包括数字的小数点对齐"}, {"节点编号": 6, "节点名称": "数字格式化处理", "测试文件": ["test_output.py"], "测试函数": ["test_floatfmt", "test_intfmt", "test_missingval", "test_floatfmt_multi", "test_column_alignment"], "起始行号": [2874, 2820, 3010, 2890, 3056], "结束行号": [2879, 2825, 3017, 2897, 3061], "重叠节点": [5], "重叠函数": ["test_column_alignment"], "备注": "提供灵活的数字格式化功能"}], "节点重叠分析": {"主要重叠节点对": [{"节点对": "2和3", "重叠文件": "test_output.py", "重叠区域": "603-620行, 1413-1430行", "重叠原因": "多种表格格式与多行单元格处理在grid和fancy_grid格式测试中都有涉及"}, {"节点对": "5和6", "重叠文件": "test_output.py", "重叠区域": "3056-3061行", "重叠原因": "列对齐与数字格式化在test_column_alignment函数中都有测试"}], "重叠程度": "约15%的测试节点存在重叠", "重叠原因总结": "测试重叠主要由于功能之间的紧密关联，如表格格式与多行处理、对齐与格式化等。重叠并不表示测试不完整，而是反映了系统中功能的相互依赖性。", "非重叠说明": "大部分节点功能相对独立，重叠主要出现在格式化相关的功能测试中。"}, "测试覆盖统计": {"总测试文件数": 8, "总测试函数数": 315, "核心功能测试行数": 2800, "专门功能测试行数": 551, "总测试行数": 3351, "测试通过情况": "315个测试用例，大部分通过，少数宽字符测试可能在Windows环境下需要特殊配置", "测试文件分布": {"test_output.py": "199个测试函数，主要测试输出格式", "test_input.py": "42个测试函数，主要测试输入数据处理", "test_internal.py": "25个测试函数，主要测试内部函数", "test_api.py": "3个测试函数，主要测试API接口", "test_cli.py": "7个测试函数，主要测试命令行工具", "test_regression.py": "35个测试函数，主要测试回归问题", "test_textwrapper.py": "14个测试函数，主要测试文本包装", "common.py": "测试工具函数"}, "测试函数统计": {"格式化测试": "199个函数（test_output.py）", "输入处理测试": "42个函数（test_input.py）", "内部功能测试": "25个函数（test_internal.py）", "API接口测试": "3个函数（test_api.py）", "命令行测试": "7个函数（test_cli.py）", "回归测试": "35个函数（test_regression.py）", "文本包装测试": "14个函数（test_textwrapper.py）"}}}
# Tox (http://tox.testrun.org/) is a tool for running tests
# in multiple virtualenvs. This configuration file will run the
# test suite on all supported python versions. To use it, "pip install tox"
# and then run "tox" from this directory.
#
# To run tests against numpy and pandas, run "tox -e py39-extra,py310-extra"
# from this directory. This will create a much bigger virtual environments
# for testing and it is disabled by default.

[tox]
envlist = lint, py{38, 39, 310, 311, 312, 313}
isolated_build = True

[gh]
python =
    3.9: py39-extra
    3.10: py310-extra
    3.11: py311-extra
    3.12: py312-extra
    3.13: py313-extra

[testenv]
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest
passenv =
    CURL_CA_BUNDLE
    REQUESTS_CA_BUNDLE
    SSL_CERT_FILE

[testenv:lint]
commands = python -m pre_commit run -a
deps =
    pre-commit

[testenv:py38]
basepython = python3.8
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest

[testenv:py38-extra]
basepython = python3.8
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest
    numpy
    pandas
    wcwidth


[testenv:py39]
basepython = python3.9
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest

[testenv:py39-extra]
basepython = python3.9
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest
    numpy
    pandas
    wcwidth


[testenv:py310]
basepython = python3.10
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest

[testenv:py310-extra]
basepython = python3.10
setenv = PYTHONDEVMODE = 1
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest
    numpy
    pandas
    wcwidth


[testenv:py311]
basepython = python3.11
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest

[testenv:py311-extra]
basepython = python3.11
setenv = PYTHONDEVMODE = 1
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest
    numpy
    pandas
    wcwidth

[testenv:py312]
basepython = python3.12
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest

[testenv:py312-extra]
basepython = python3.12
setenv = PYTHONDEVMODE = 1
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest
    numpy
    pandas
    wcwidth

[testenv:py313]
basepython = python3.13
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest

[testenv:py313-extra]
basepython = python3.13
setenv = PYTHONDEVMODE = 1
commands = pytest -v --doctest-modules --ignore benchmark {posargs}
deps =
    pytest
    numpy
    pandas
    wcwidth

[flake8]
max-complexity = 22
max-line-length = 99
ignore = E203, W503, C901, E402, B011
